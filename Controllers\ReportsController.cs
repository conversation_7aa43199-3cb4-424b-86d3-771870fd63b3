﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using EndDayReports.model;
using EndDayReports.Data;
using System.Text;

namespace EndDayReports.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ReportsController : ControllerBase
    {
        private readonly EndDayReportsContext _context;

        public ReportsController(EndDayReportsContext context)
        {
            _context = context;
       
        }

        [HttpGet("{id}/download/cash")]
        public async Task<IActionResult> DownloadCashDepositSlip(int id)
        {
            var report = await _context.EndDayReport.FindAsync(id);
            if (report == null || report.CashDepositSlipPdf == null)
                return NotFound();

            return File(
                fileContents: report.CashDepositSlipPdf,
                contentType: "application/pdf",
                fileDownloadName: report.CashDepositFileName ?? $"cash-deposit-{id}.pdf"
            );
        }

        [HttpGet("{id}/download/paidout")]
        public async Task<IActionResult> DownloadPaidOutDepositSlip(int id)
        {
            var report = await _context.EndDayReport.FindAsync(id);
            if (report == null || report.PaidOutDepositSlipPdf == null)
                return NotFound();

            return File(
                fileContents: report.PaidOutDepositSlipPdf,
                contentType: "application/pdf",
                fileDownloadName: report.PaidOutDepositFileName ?? $"paidout-deposit-{id}.pdf"
            );
        }


        [HttpGet("{id}/download/approvedcredit")]
        public async Task<IActionResult> DownloadApprovedCreditDepositSlip(int id)
        {
            var report = await _context.EndDayReport.FindAsync(id);
            if (report == null || report.ApprovedCreditDepositSlipPdf == null)
                return NotFound();

            return File(
                fileContents: report.ApprovedCreditDepositSlipPdf,
                contentType: "application/pdf",
                fileDownloadName: report.ApprovedCreditDepositFileName ?? $"approvedcredit-deposit-{id}.pdf"
            );
        }


        [HttpGet("{id}/download/check")]
        public async Task<IActionResult> DownloadCheckDepositSlip(int id)
        {
            var report = await _context.EndDayReport.FindAsync(id);
            if (report == null || report.CheckDepositSlipPdf == null)
                return NotFound();

            return File(
                fileContents: report.CheckDepositSlipPdf,
                contentType: "application/pdf",
                fileDownloadName: report.CashDepositFileName ?? $"cash-deposit-{id}.pdf"
            );
        }

        [HttpGet("{id}/export/csv")]
        public async Task<IActionResult> ExportToCsv(int id)
        {
            var report = await _context.EndDayReport
                .Include(r => r.OverUnderEntries)
                .FirstOrDefaultAsync(r => r.Id == id);

            if (report == null)
                return NotFound();

            var csv = GenerateCsvContent(report);
            var bytes = Encoding.UTF8.GetBytes(csv);

            var fileName = $"EndDayReport_{report.CreatedDateTime:yyyy-MM-dd}_{report.Id}.csv";

            return File(
                fileContents: bytes,
                contentType: "text/csv",
                fileDownloadName: fileName
            );
        }

        [HttpGet("export/csv")]
        public async Task<IActionResult> ExportAllToCsv()
        {
            var reports = await _context.EndDayReport
                .Include(r => r.OverUnderEntries)
                .OrderByDescending(r => r.CreatedDateTime)
                .ToListAsync();

            if (!reports.Any())
                return NotFound("No reports found to export.");

            var csv = GenerateMultipleReportsCsvContent(reports);
            var bytes = Encoding.UTF8.GetBytes(csv);

            var fileName = $"EndDayReports_Export_{DateTime.Now:yyyy-MM-dd_HHmmss}.csv";

            return File(
                fileContents: bytes,
                contentType: "text/csv",
                fileDownloadName: fileName
            );
        }

        private string GenerateCsvContent(EndDayReport report)
        {
            var csv = new StringBuilder();

            // Add header
            csv.AppendLine("Field,Value");

            // Basic Information
            csv.AppendLine($"Report ID,{report.Id}");
            csv.AppendLine($"Created By,\"{report.CreatedByEmployeeName}\"");
            csv.AppendLine($"Created Date/Time,{report.CreatedDateTime:yyyy-MM-dd HH:mm:ss}");
            csv.AppendLine($"Shift Start Time,{report.ShiftTimeStartStr}");
            csv.AppendLine($"Shift End Time,{report.ShiftTimeEndStr}");

            // Pump Sales
            csv.AppendLine("--- PUMP SALES ---,");
            csv.AppendLine($"K1 Dollars,{report.PumpK1Dollars:F2}");
            csv.AppendLine($"K1 Gallons,{report.PumpK1Gallons:F2}");
            csv.AppendLine($"Off Road Diesel Dollars,{report.PumpOffRoadDieselDollars:F2}");
            csv.AppendLine($"Off Road Diesel Gallons,{report.PumpOffRoadDieselGallons:F2}");
            csv.AppendLine($"Unleaded Premium Dollars,{report.PumpUnleadedPremiumDollars:F2}");
            csv.AppendLine($"Unleaded Premium Gallons,{report.PumpUnleadedPremiumGallons:F2}");
            csv.AppendLine($"Unleaded Plus Dollars,{report.PumpUnleadedPlusDollars:F2}");
            csv.AppendLine($"Unleaded Plus Gallons,{report.PumpUnleadedPlusGallons:F2}");
            csv.AppendLine($"Unleaded Regular Dollars,{report.PumpUnleadedRegularDollars:F2}");
            csv.AppendLine($"Unleaded Regular Gallons,{report.PumpUnleadedRegularGallons:F2}");
            csv.AppendLine($"Diesel Dollars,{report.PumpDieselDollars:F2}");
            csv.AppendLine($"Diesel Gallons,{report.PumpDieselGallons:F2}");
            csv.AppendLine($"Total Pump Sales,{report.TotalPumpSales:F2}");
            csv.AppendLine($"Total Pump Gallons,{report.TotalPumpGallons:F2}");

            // Product Sales
            csv.AppendLine("--- PRODUCT SALES ---,");
            csv.AppendLine($"Ice,{report.Ice:F2}");
            csv.AppendLine($"Beer,{report.Beer:F2}");
            csv.AppendLine($"Groceries,{report.Groceries:F2}");
            csv.AppendLine($"Deli,{report.Deli:F2}");
            csv.AppendLine($"Cigarettes,{report.Cigarettes:F2}");
            csv.AppendLine($"Meat,{report.Meat:F2}");
            csv.AppendLine($"Non-Food,{report.NonFood:F2}");
            csv.AppendLine($"Gasoline,{report.Gasoline:F2}");
            csv.AppendLine($"Chicken,{report.Chicken:F2}");
            csv.AppendLine($"HBA,{report.HBA:F2}");
            csv.AppendLine($"Lotto,{report.Lotto:F2}");
            csv.AppendLine($"Pizza,{report.Pizza:F2}");
            csv.AppendLine($"Money Order,{report.MoneyOrder:F2}");
            csv.AppendLine($"Lottery,{report.Lottery:F2}");
            csv.AppendLine($"Farm Tax,{report.FarmTax:F2}");
            csv.AppendLine($"Sales Tax,{report.SalesTax:F2}");
            csv.AppendLine($"Total Tax,{report.TotalTax:F2}");
            csv.AppendLine($"Total Products Sales,{report.TotalProductsSales:F2}");

            // Credits/Tender
            csv.AppendLine("--- CREDITS/TENDER ---,");
            csv.AppendLine($"Credit Card,{report.CreditCard:F2}");
            csv.AppendLine($"Debit Card,{report.DebitCard:F2}");
            csv.AppendLine($"Food Stamps,{report.FoodStamps:F2}");
            csv.AppendLine($"Coupons,{report.Coupons:F2}");
            csv.AppendLine($"Approved Credit,{report.ApprovedCredit:F2}");
            csv.AppendLine($"Paid Outs,{report.PaidOuts:F2}");
            csv.AppendLine($"Owe Paid Bag,{report.OwePaidBag:F2}");
            csv.AppendLine($"Void and Refund,{report.VoidAndRefund:F2}");
            csv.AppendLine($"ATM,{report.ATM:F2}");
            csv.AppendLine($"Telecheck,{report.Telecheck:F2}");
            csv.AppendLine($"Cash,{report.Cash:F2}");
            csv.AppendLine($"Check,{report.Check:F2}");
            csv.AppendLine($"Total Credits,{report.TotalCredits:F2}");

            // Tax Report
            csv.AppendLine("--- TAX REPORT ---,");
            csv.AppendLine($"Low Tax Taxable Sales,{report.LowTaxTaxableSales:F2}");
            csv.AppendLine($"Low Tax Non-Taxable Sales,{report.LowTaxNonTaxableSales:F2}");
            csv.AppendLine($"High Tax Taxable Sales,{report.HighTaxTaxableSales:F2}");
            csv.AppendLine($"High Tax Non-Taxable Sales,{report.HighTaxNonTaxableSales:F2}");

            // Totals
            csv.AppendLine("--- TOTALS ---,");
            csv.AppendLine($"Total Over/Under,{report.TotalOverUnder:F2}");
            csv.AppendLine($"Grand Total,{report.GrandTotal:F2}");

            // Over/Under Entries
            if (report.OverUnderEntries.Any())
            {
                csv.AppendLine("--- OVER/UNDER ENTRIES ---,");
                foreach (var entry in report.OverUnderEntries)
                {
                    csv.AppendLine($"Cashier: {entry.CashierName},{entry.OverAmount:F2}");
                }
            }

            // Notes
            if (!string.IsNullOrEmpty(report.Notes))
            {
                csv.AppendLine("--- NOTES ---,");
                csv.AppendLine($"Notes,\"{report.Notes.Replace("\"", "\"\"")}\"");
            }

            return csv.ToString();
        }

        private string GenerateMultipleReportsCsvContent(List<EndDayReport> reports)
        {
            var csv = new StringBuilder();

            // Add header for multiple reports
            csv.AppendLine("Report ID,Created By,Created Date/Time,Shift Start,Shift End,Total Pump Sales,Total Products Sales,Total Credits,Total Over/Under,Grand Total,Notes");

            foreach (var report in reports)
            {
                var notes = report.Notes?.Replace("\"", "\"\"") ?? "";
                csv.AppendLine($"{report.Id}," +
                              $"\"{report.CreatedByEmployeeName}\"," +
                              $"{report.CreatedDateTime:yyyy-MM-dd HH:mm:ss}," +
                              $"{report.ShiftTimeStartStr}," +
                              $"{report.ShiftTimeEndStr}," +
                              $"{report.TotalPumpSales:F2}," +
                              $"{report.TotalProductsSales:F2}," +
                              $"{report.TotalCredits:F2}," +
                              $"{report.TotalOverUnder:F2}," +
                              $"{report.GrandTotal:F2}," +
                              $"\"{notes}\"");
            }

            return csv.ToString();
        }
    }
}
