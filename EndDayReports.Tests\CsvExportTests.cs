using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using EndDayReports.Data;
using EndDayReports.model;
using System.Net;
using System.Text;
using Xunit;

namespace EndDayReports.Tests
{
    public class CsvExportTests : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly WebApplicationFactory<Program> _factory;

        public CsvExportTests(WebApplicationFactory<Program> factory)
        {
            _factory = factory;
        }

        [Fact]
        public async Task ExportToCsv_WithValidId_ReturnsCSVFile()
        {
            // Arrange
            var client = _factory.WithWebHostBuilder(builder =>
            {
                builder.ConfigureServices(services =>
                {
                    // Remove the existing DbContext registration
                    var descriptor = services.SingleOrDefault(
                        d => d.ServiceType == typeof(DbContextOptions<EndDayReportsContext>));
                    if (descriptor != null)
                        services.Remove(descriptor);

                    // Add InMemory database for testing
                    services.AddDbContext<EndDayReportsContext>(options =>
                    {
                        options.UseInMemoryDatabase("TestDb");
                    });
                });
            }).CreateClient();

            // Seed test data
            using var scope = _factory.Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<EndDayReportsContext>();
            
            var testReport = new EndDayReport
            {
                Id = 1,
                CreatedByEmployeeName = "Test Employee",
                CreatedDateTime = DateTime.Now,
                ShiftTimeStart = TimeSpan.FromHours(8),
                ShiftTimeEnd = TimeSpan.FromHours(16),
                PumpK1Dollars = 100.50m,
                PumpK1Gallons = 25.75m,
                Ice = 15.00m,
                Beer = 45.25m,
                CreditCard = 75.00m,
                Cash = 85.75m,
                Notes = "Test notes for CSV export"
            };

            context.EndDayReport.Add(testReport);
            await context.SaveChangesAsync();

            // Act
            var response = await client.GetAsync($"/api/reports/{testReport.Id}/export/csv");

            // Assert
            Assert.Equal(HttpStatusCode.OK, response.StatusCode);
            Assert.Equal("text/csv", response.Content.Headers.ContentType?.MediaType);
            
            var content = await response.Content.ReadAsStringAsync();
            
            // Verify CSV content contains expected data
            Assert.Contains("Field,Value", content);
            Assert.Contains("Report ID,1", content);
            Assert.Contains("Created By,\"Test Employee\"", content);
            Assert.Contains("K1 Dollars,100.50", content);
            Assert.Contains("K1 Gallons,25.75", content);
            Assert.Contains("Ice,15.00", content);
            Assert.Contains("Beer,45.25", content);
            Assert.Contains("Credit Card,75.00", content);
            Assert.Contains("Cash,85.75", content);
            Assert.Contains("Notes,\"Test notes for CSV export\"", content);
        }

        [Fact]
        public async Task ExportToCsv_WithInvalidId_ReturnsNotFound()
        {
            // Arrange
            var client = _factory.CreateClient();

            // Act
            var response = await client.GetAsync("/api/reports/999/export/csv");

            // Assert
            Assert.Equal(HttpStatusCode.NotFound, response.StatusCode);
        }

        [Fact]
        public async Task ExportAllToCsv_WithReports_ReturnsCSVFile()
        {
            // Arrange
            var client = _factory.WithWebHostBuilder(builder =>
            {
                builder.ConfigureServices(services =>
                {
                    var descriptor = services.SingleOrDefault(
                        d => d.ServiceType == typeof(DbContextOptions<EndDayReportsContext>));
                    if (descriptor != null)
                        services.Remove(descriptor);

                    services.AddDbContext<EndDayReportsContext>(options =>
                    {
                        options.UseInMemoryDatabase("TestDb2");
                    });
                });
            }).CreateClient();

            // Seed test data
            using var scope = _factory.Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<EndDayReportsContext>();
            
            var reports = new List<EndDayReport>
            {
                new EndDayReport
                {
                    Id = 1,
                    CreatedByEmployeeName = "Employee 1",
                    CreatedDateTime = DateTime.Now.AddDays(-1),
                    TotalPumpSales = 100.00m,
                    TotalProductsSales = 200.00m
                },
                new EndDayReport
                {
                    Id = 2,
                    CreatedByEmployeeName = "Employee 2",
                    CreatedDateTime = DateTime.Now,
                    TotalPumpSales = 150.00m,
                    TotalProductsSales = 250.00m
                }
            };

            context.EndDayReport.AddRange(reports);
            await context.SaveChangesAsync();

            // Act
            var response = await client.GetAsync("/api/reports/export/csv");

            // Assert
            Assert.Equal(HttpStatusCode.OK, response.StatusCode);
            Assert.Equal("text/csv", response.Content.Headers.ContentType?.MediaType);
            
            var content = await response.Content.ReadAsStringAsync();
            
            // Verify CSV content contains expected headers and data
            Assert.Contains("Report ID,Created By,Created Date/Time", content);
            Assert.Contains("1,\"Employee 1\"", content);
            Assert.Contains("2,\"Employee 2\"", content);
        }
    }
}
